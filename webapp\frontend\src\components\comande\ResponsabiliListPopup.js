import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Assignment as AssignIcon
} from '@mui/icons-material';

const ResponsabiliListPopup = ({ 
  open, 
  onClose, 
  responsabili, 
  comandePerResponsabile,
  onEditResponsabile, 
  onDeleteResponsabile,
  loading = false,
  error = null 
}) => {

  const handleDeleteClick = async (responsabile) => {
    const comandeAssegnate = comandePerResponsabile[responsabile.id_responsabile] || [];
    
    if (comandeAssegnate.length > 0) {
      alert(`Impossibile eliminare il responsabile "${responsabile.nome_responsabile}". Ha ${comandeAssegnate.length} comande assegnate.`);
      return;
    }

    const confirmDelete = window.confirm(
      `Sei sicuro di voler eliminare il responsabile "${responsabile.nome_responsabile}"?`
    );
    
    if (confirmDelete) {
      onDeleteResponsabile(responsabile.id_responsabile);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Lista Responsabili
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" py={4}>
            <CircularProgress />
          </Box>
        ) : (
          <TableContainer component={Paper} elevation={0} sx={{ border: '1px solid', borderColor: 'grey.200' }}>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600 }}>Nome Responsabile</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Email</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Telefono</TableCell>
                  <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Comande</TableCell>
                  <TableCell sx={{ fontWeight: 600, textAlign: 'center' }}>Azioni</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {responsabili.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body2" color="text.secondary">
                        Nessun responsabile configurato
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  responsabili.map((responsabile) => {
                    const comandeAssegnate = comandePerResponsabile[responsabile.id_responsabile] || [];
                    const canDelete = comandeAssegnate.length === 0;
                    
                    return (
                      <TableRow 
                        key={responsabile.id_responsabile}
                        sx={{ 
                          '&:hover': { 
                            backgroundColor: 'rgba(33, 150, 243, 0.1)' 
                          } 
                        }}
                      >
                        <TableCell>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {responsabile.nome_responsabile}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {responsabile.email || '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {responsabile.telefono || '-'}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ textAlign: 'center' }}>
                          <Chip
                            icon={<AssignIcon />}
                            label={comandeAssegnate.length}
                            size="small"
                            color={comandeAssegnate.length > 0 ? 'primary' : 'default'}
                            variant="outlined"
                            sx={{
                              fontWeight: 500,
                              backgroundColor: comandeAssegnate.length > 0 ? 'rgba(33, 150, 243, 0.1)' : 'transparent',
                              color: comandeAssegnate.length > 0 ? '#1976d2' : 'text.secondary'
                            }}
                          />
                        </TableCell>
                        <TableCell sx={{ textAlign: 'center' }}>
                          <Box display="flex" justifyContent="center" gap={0.5}>
                            <Tooltip title="Modifica responsabile">
                              <IconButton
                                size="small"
                                onClick={() => onEditResponsabile(responsabile)}
                                sx={{
                                  color: '#6c757d',
                                  '&:hover': {
                                    backgroundColor: '#e9ecef'
                                  }
                                }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title={canDelete ? "Elimina responsabile" : "Impossibile eliminare: ha comande assegnate"}>
                              <span>
                                <IconButton
                                  size="small"
                                  onClick={() => handleDeleteClick(responsabile)}
                                  disabled={!canDelete}
                                  sx={{
                                    color: canDelete ? '#6c757d' : '#ccc',
                                    '&:hover': {
                                      backgroundColor: canDelete ? '#e9ecef' : 'transparent'
                                    }
                                  }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </span>
                            </Tooltip>
                          </Box>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </DialogContent>
      
      <DialogActions sx={{ p: 3, pt: 2 }}>
        <Button
          onClick={onClose}
          sx={{ 
            textTransform: 'none',
            fontWeight: 500,
            px: 3
          }}
        >
          Chiudi
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ResponsabiliListPopup;
